@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Amiri:wght@400;700&family=Inter:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";
@tailwind base;
@tailwind components;
@tailwind utilities;

html { direction: rtl; }
body {
  direction: rtl;
  background: linear-gradient(135deg, #f7f3e9 0%, #fefefe 25%, #f5e6d3 50%, #fffff0 75%, #fefefe 100%);
  background-attachment: fixed;
  font-family: 'Cairo', 'Inter', system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue', Arial;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

/* Luxury Background Pattern */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(201, 169, 110, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 215, 0, 0.05) 0%, transparent 50%),
    linear-gradient(45deg, transparent 40%, rgba(229, 228, 226, 0.03) 50%, transparent 60%);
  pointer-events: none;
  z-index: -1;
}

/* Luxurious Wedding Palace Theme - Premium Gold & Platinum */
.client-theme {
  /* Primary Gold Palette - Rich & Sophisticated */
  --primary-gold: #c9a96e; /* Champagne gold */
  --primary-gold-light: #f5e6d3; /* Soft champagne */
  --primary-gold-dark: #8b6914; /* Deep gold */
  --royal-gold: #ffd700; /* Royal gold accent */
  --antique-gold: #b8860b; /* Antique gold */

  /* Platinum & White Palette */
  --platinum: #e5e4e2; /* Platinum silver */
  --pearl-white: #fefefe; /* Pearl white */
  --ivory: #fffff0; /* Ivory cream */
  --champagne-white: #f7f3e9; /* Champagne white */

  /* Sophisticated Dark Accents */
  --charcoal: #36454f; /* Charcoal gray */
  --espresso: #3c2415; /* Espresso brown */
  --midnight: #191970; /* Midnight blue */
  --obsidian: #0f0f23; /* Deep obsidian */

  /* Text Colors */
  --text-luxury: #2c1810; /* Luxury brown */
  --text-elegant: #4a4a4a; /* Elegant gray */
  --text-subtle: #8b7355; /* Subtle brown */

  /* Advanced Shadows & Effects */
  --shadow-luxury: rgba(201, 169, 110, 0.25);
  --shadow-deep: rgba(44, 24, 16, 0.15);
  --shadow-glow: rgba(255, 215, 0, 0.3);
  --shadow-platinum: rgba(229, 228, 226, 0.4);

  /* Premium Gradients */
  --gradient-luxury: linear-gradient(135deg, #c9a96e 0%, #ffd700 50%, #f5e6d3 100%);
  --gradient-platinum: linear-gradient(135deg, #e5e4e2 0%, #fefefe 50%, #f7f3e9 100%);
  --gradient-royal: linear-gradient(45deg, #ffd700 0%, #c9a96e 25%, #ffd700 50%, #c9a96e 75%, #ffd700 100%);
  --gradient-sunset: linear-gradient(135deg, #ff6b35 0%, #f7931e 25%, #ffd700 50%, #c9a96e 100%);
  --gradient-aurora: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #c9a96e 50%, #ffd700 100%);

  /* Glass & Blur Effects */
  --glass-bg: rgba(254, 254, 254, 0.1);
  --glass-border: rgba(201, 169, 110, 0.2);
  --blur-light: blur(10px);
  --blur-heavy: blur(20px);
}

/* Luxurious TopBar with Glass Morphism */
.client-theme .topbar {
  background: linear-gradient(135deg,
    rgba(254, 254, 254, 0.95) 0%,
    rgba(247, 243, 233, 0.9) 50%,
    rgba(245, 230, 211, 0.95) 100%);
  backdrop-filter: var(--blur-light);
  border-bottom: 3px solid transparent;
  border-image: var(--gradient-luxury) 1;
  box-shadow:
    0 8px 32px var(--shadow-luxury),
    0 2px 16px var(--shadow-platinum),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.client-theme .topbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-royal);
  opacity: 0;
  transition: all 0.6s ease;
  z-index: -1;
}

.client-theme .topbar:hover::before {
  left: 100%;
  opacity: 0.1;
}

.client-theme .topbar .text-lg {
  color: var(--text-luxury);
  font-family: 'Amiri', serif;
  font-weight: 700;
  text-shadow:
    0 2px 4px rgba(0,0,0,0.1),
    0 0 20px rgba(201, 169, 110, 0.3);
  background: var(--gradient-luxury);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.client-theme .topbar a {
  color: var(--primary-gold);
  font-weight: 600;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  padding: 8px 16px;
  border-radius: 12px;
  overflow: hidden;
}

.client-theme .topbar a::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-luxury);
  opacity: 0.1;
  transition: all 0.3s ease;
  z-index: -1;
}

.client-theme .topbar a:hover {
  color: var(--royal-gold);
  transform: translateY(-2px) scale(1.05);
  text-shadow:
    0 4px 8px var(--shadow-glow),
    0 0 20px rgba(255, 215, 0, 0.5);
  box-shadow: 0 8px 25px var(--shadow-luxury);
}

.client-theme .topbar a:hover::before {
  left: 0;
  opacity: 0.2;
}

/* Magnificent Sidebar with Advanced Glass Morphism */
.client-theme aside {
  background: linear-gradient(180deg,
    rgba(254, 254, 254, 0.95) 0%,
    rgba(247, 243, 233, 0.9) 30%,
    rgba(245, 230, 211, 0.95) 70%,
    rgba(229, 228, 226, 0.9) 100%);
  backdrop-filter: var(--blur-light);
  border-right: 4px solid transparent;
  border-image: var(--gradient-luxury) 1;
  box-shadow:
    8px 0 40px var(--shadow-luxury),
    4px 0 20px var(--shadow-platinum),
    inset -1px 0 0 rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.client-theme aside::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 2px;
  height: 100%;
  background: var(--gradient-royal);
  animation: shimmerVertical 3s ease-in-out infinite;
}

.client-theme aside a {
  color: var(--text-luxury);
  font-weight: 500;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 16px;
  margin: 6px 12px;
  padding: 12px 20px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(201, 169, 110, 0.1);
}

.client-theme aside a::before {
  content: '';
  position: absolute;
  top: 0;
  right: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-luxury);
  opacity: 0;
  transition: all 0.5s ease;
  z-index: -1;
}

.client-theme aside a::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 10px;
  width: 6px;
  height: 6px;
  background: var(--royal-gold);
  border-radius: 50%;
  transform: translateY(-50%) scale(0);
  transition: all 0.3s ease;
  box-shadow: 0 0 10px var(--royal-gold);
}

.client-theme aside a:hover {
  background: rgba(201, 169, 110, 0.1);
  color: var(--primary-gold);
  transform: translateX(-8px) scale(1.02);
  box-shadow:
    0 8px 25px var(--shadow-luxury),
    0 0 20px rgba(255, 215, 0, 0.2);
  border-color: var(--primary-gold);
}

.client-theme aside a:hover::before {
  right: 0;
  opacity: 0.15;
}

.client-theme aside a:hover::after {
  transform: translateY(-50%) scale(1);
}

/* Active link with premium styling */
.client-theme aside a.active {
  background: var(--gradient-luxury);
  color: var(--pearl-white);
  border-right: 6px solid var(--royal-gold);
  box-shadow:
    0 8px 30px var(--shadow-luxury),
    0 0 25px rgba(255, 215, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  font-weight: 700;
  transform: translateX(-4px);
}

.client-theme aside a.active::after {
  transform: translateY(-50%) scale(1.2);
  background: var(--pearl-white);
  box-shadow: 0 0 15px var(--pearl-white);
}

/* Premium Buttons with Luxury Effects */
.client-theme .btn-primary {
  background: var(--gradient-luxury);
  color: var(--pearl-white);
  border: 3px solid transparent;
  border-image: var(--gradient-royal) 1;
  font-weight: 700;
  font-family: 'Cairo', sans-serif;
  padding: 16px 32px;
  border-radius: 16px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 8px 25px var(--shadow-luxury),
    0 4px 15px var(--shadow-glow),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.client-theme .btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-royal);
  transition: all 0.5s ease;
  z-index: -1;
}

.client-theme .btn-primary::after {
  content: '✨';
  position: absolute;
  top: 50%;
  right: 16px;
  transform: translateY(-50%) scale(0);
  transition: all 0.3s ease;
  font-size: 16px;
}

.client-theme .btn-primary:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow:
    0 15px 40px var(--shadow-luxury),
    0 8px 30px var(--shadow-glow),
    0 0 30px rgba(255, 215, 0, 0.4);
  animation: luxuryPulse 2s ease-in-out infinite;
}

.client-theme .btn-primary:hover::before {
  left: 0;
}

.client-theme .btn-primary:hover::after {
  transform: translateY(-50%) scale(1);
  animation: diamondSparkle 1s ease-in-out infinite;
}

.client-theme .btn-primary:active {
  transform: translateY(-2px) scale(1.02);
}

/* Luxury Cards with Premium Glass Effect */
.client-theme .card {
  background: linear-gradient(135deg,
    rgba(254, 254, 254, 0.95) 0%,
    rgba(247, 243, 233, 0.9) 50%,
    rgba(245, 230, 211, 0.95) 100%);
  backdrop-filter: var(--blur-light);
  border: 2px solid transparent;
  border-image: var(--gradient-luxury) 1;
  border-radius: 24px;
  box-shadow:
    0 20px 60px var(--shadow-luxury),
    0 8px 30px var(--shadow-platinum),
    inset 0 1px 0 rgba(255, 255, 255, 0.8),
    inset 0 -1px 0 rgba(201, 169, 110, 0.1);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
}

.client-theme .card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    from 0deg,
    transparent 0deg,
    rgba(255, 215, 0, 0.1) 60deg,
    transparent 120deg,
    rgba(201, 169, 110, 0.1) 180deg,
    transparent 240deg,
    rgba(255, 215, 0, 0.1) 300deg,
    transparent 360deg
  );
  animation: diamondSparkle 8s linear infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.client-theme .card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--gradient-royal);
  opacity: 0.6;
}

.client-theme .card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow:
    0 30px 80px var(--shadow-luxury),
    0 15px 50px var(--shadow-glow),
    0 0 40px rgba(255, 215, 0, 0.2);
  border-image: var(--gradient-royal) 1;
}

.client-theme .card:hover::before {
  opacity: 1;
}

.client-theme .heading-gold {
  color: var(--primary-gold);
  font-family: 'Amiri', serif;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.client-theme .badge-gold {
  background: var(--gradient-gold);
  color: var(--accent-white);
  border: 1px solid var(--primary-gold);
  font-weight: 600;
  box-shadow: 0 2px 8px var(--shadow-gold);
}

/* Elegant spinner with gold theme */
.client-theme .spinner {
  border-color: var(--primary-gold);
  border-right-color: transparent;
  animation: spin 1s linear infinite;
}

/* Icon circle with gold background */
.client-theme .icon-circle-gold {
  background: var(--gradient-gold);
  color: var(--accent-white);
  box-shadow: 0 4px 15px var(--shadow-gold);
  transition: all 0.3s ease;
}

.client-theme .icon-circle-gold:hover {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 6px 20px var(--shadow-gold);
}

/* Fade-in Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes goldShimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

@keyframes shimmerVertical {
  0%, 100% {
    opacity: 0.3;
    transform: scaleY(1);
  }
  50% {
    opacity: 1;
    transform: scaleY(1.5);
  }
}

@keyframes luxuryPulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(201, 169, 110, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(255, 215, 0, 0.5);
  }
}

@keyframes royalGlow {
  0%, 100% {
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
  }
  50% {
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.8), 0 0 30px rgba(201, 169, 110, 0.6);
  }
}

@keyframes diamondSparkle {
  0%, 100% {
    transform: rotate(0deg) scale(1);
    opacity: 0.7;
  }
  25% {
    transform: rotate(90deg) scale(1.1);
    opacity: 1;
  }
  50% {
    transform: rotate(180deg) scale(0.9);
    opacity: 0.8;
  }
  75% {
    transform: rotate(270deg) scale(1.1);
    opacity: 1;
  }
}

@keyframes elegantFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-8px) rotate(1deg);
  }
  66% {
    transform: translateY(-4px) rotate(-1deg);
  }
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

.gold-shimmer {
  background: linear-gradient(90deg, var(--primary-gold) 0%, var(--primary-gold-light) 50%, var(--primary-gold) 100%);
  background-size: 200% 100%;
  animation: goldShimmer 2s ease-in-out infinite;
}

/* Typography improvements */
.client-theme h1, .client-theme h2, .client-theme h3 {
  font-family: 'Amiri', serif;
  color: var(--text-primary);
  font-weight: 700;
}

.client-theme p, .client-theme span, .client-theme div {
  font-family: 'Cairo', sans-serif;
  color: var(--text-secondary);
}

/* Parallax effect container */
.parallax-container {
  position: relative;
  overflow: hidden;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.parallax-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(255, 255, 255, 0.9) 100%);
  backdrop-filter: blur(2px);
}

/* Enhanced form inputs */
.client-theme input, .client-theme textarea, .client-theme select {
  border: 2px solid var(--primary-gold-light);
  border-radius: 8px;
  padding: 12px 16px;
  font-family: 'Cairo', sans-serif;
  transition: all 0.3s ease;
  background: var(--accent-white);
}

.client-theme input:focus, .client-theme textarea:focus, .client-theme select:focus {
  border-color: var(--primary-gold);
  box-shadow: 0 0 0 3px var(--shadow-gold);
  outline: none;
  transform: translateY(-1px);
}

/* Elegant table styling */
.client-theme table {
  background: var(--accent-white);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 25px var(--shadow-gold);
}

.client-theme th {
  background: var(--gradient-gold);
  color: var(--accent-white);
  font-family: 'Amiri', serif;
  font-weight: 700;
  padding: 16px;
}

.client-theme td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--primary-gold-light);
  font-family: 'Cairo', sans-serif;
  color: var(--text-secondary);
}

.client-theme tr:hover {
  background: var(--accent-cream);
  transition: all 0.3s ease;
}

/* Advanced visual effects */
.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(212, 175, 55, 0.2);
  box-shadow: 0 8px 32px rgba(212, 175, 55, 0.1);
}

.gold-gradient-text {
  background: var(--gradient-gold);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* Floating elements */
.floating {
  animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Elegant shadows */
.shadow-gold-lg {
  box-shadow: 0 20px 40px var(--shadow-gold);
}

.shadow-gold-xl {
  box-shadow: 0 25px 50px var(--shadow-gold);
}

/* Responsive design improvements */
@media (max-width: 768px) {
  .client-theme .topbar {
    padding: 8px 16px;
  }

  .client-theme aside {
    width: 100%;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .client-theme aside.open {
    transform: translateX(0);
  }

  .client-theme .card {
    margin: 8px;
    border-radius: 8px;
  }
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Status indicators */
.status-success {
  background: linear-gradient(135deg, #10b981, #34d399);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.status-warning {
  background: linear-gradient(135deg, #f59e0b, #fbbf24);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.status-error {
  background: linear-gradient(135deg, #ef4444, #f87171);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

/* Enhanced buttons */
.btn-gold {
  background: var(--gradient-gold);
  color: var(--accent-white);
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-family: 'Cairo', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px var(--shadow-gold);
}

.btn-gold:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow-gold);
  background: var(--primary-gold-dark);
}

.btn-outline-gold {
  background: transparent;
  color: var(--primary-gold);
  border: 2px solid var(--primary-gold);
  padding: 10px 22px;
  border-radius: 8px;
  font-weight: 600;
  font-family: 'Cairo', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-outline-gold:hover {
  background: var(--gradient-gold);
  color: var(--accent-white);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px var(--shadow-gold);
}

/* Modal and overlay improvements */
.modal-overlay {
  background: rgba(44, 24, 16, 0.7);
  backdrop-filter: blur(5px);
}

.modal-content {
  background: var(--accent-white);
  border-radius: 16px;
  box-shadow: 0 25px 50px rgba(44, 24, 16, 0.3);
  border: 2px solid var(--primary-gold-light);
}

/* Notification styles */
.notification {
  border-radius: 12px;
  padding: 16px 20px;
  margin: 8px 0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  animation: slideInRight 0.3s ease-out;
}

.notification-success {
  background: linear-gradient(135deg, #ecfdf5, #d1fae5);
  border-left: 4px solid #10b981;
  color: #065f46;
}

.notification-warning {
  background: linear-gradient(135deg, #fffbeb, #fef3c7);
  border-left: 4px solid #f59e0b;
  color: #92400e;
}

.notification-error {
  background: linear-gradient(135deg, #fef2f2, #fecaca);
  border-left: 4px solid #ef4444;
  color: #991b1b;
}

/* Arabic text enhancements */
.arabic-text {
  font-family: 'Amiri', serif;
  line-height: 1.8;
  letter-spacing: 0.5px;
}

.english-text {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
}

/* Print styles */
@media print {
  .client-theme {
    background: white !important;
  }

  .client-theme .topbar,
  .client-theme aside {
    display: none !important;
  }

  .client-theme .card {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}

/* Override all default button colors to match gold theme */
.client-theme button {
  background: var(--gradient-gold);
  color: var(--accent-white);
  border: 2px solid var(--primary-gold);
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px var(--shadow-gold);
  border-radius: 8px;
  padding: 8px 16px;
  font-family: 'Cairo', sans-serif;
}

.client-theme button:hover {
  background: var(--primary-gold-dark);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px var(--shadow-gold);
}

/* Override specific button types */
.client-theme button.bg-red-500,
.client-theme button.bg-red-600,
.client-theme .bg-red-500,
.client-theme .bg-red-600 {
  background: linear-gradient(135deg, #dc2626, #ef4444) !important;
  border-color: #dc2626 !important;
}

.client-theme button.bg-red-500:hover,
.client-theme button.bg-red-600:hover,
.client-theme .bg-red-500:hover,
.client-theme .bg-red-600:hover {
  background: linear-gradient(135deg, #b91c1c, #dc2626) !important;
}

.client-theme button.bg-blue-500,
.client-theme button.bg-blue-600,
.client-theme .bg-blue-500,
.client-theme .bg-blue-600 {
  background: var(--gradient-gold) !important;
  border-color: var(--primary-gold) !important;
  color: var(--accent-white) !important;
}

.client-theme button.bg-blue-500:hover,
.client-theme button.bg-blue-600:hover,
.client-theme .bg-blue-500:hover,
.client-theme .bg-blue-600:hover {
  background: var(--primary-gold-dark) !important;
}

.client-theme button.bg-green-500,
.client-theme button.bg-green-600,
.client-theme .bg-green-500,
.client-theme .bg-green-600 {
  background: linear-gradient(135deg, #059669, #10b981) !important;
  border-color: #059669 !important;
}

.client-theme button.bg-green-500:hover,
.client-theme button.bg-green-600:hover,
.client-theme .bg-green-500:hover,
.client-theme .bg-green-600:hover {
  background: linear-gradient(135deg, #047857, #059669) !important;
}

/* Magnificent Table Headers */
.client-theme table thead th,
.client-theme .table-header,
.client-theme th {
  background: var(--gradient-luxury) !important;
  color: var(--pearl-white) !important;
  font-family: 'Amiri', serif !important;
  font-weight: 700 !important;
  padding: 20px 24px !important;
  border: none !important;
  position: relative !important;
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.client-theme table thead th::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-royal);
  animation: shimmerVertical 3s ease-in-out infinite;
}

/* Elegant Table Rows with Luxury Styling */
.client-theme table tbody tr:nth-child(even),
.client-theme .table-row-even {
  background: linear-gradient(90deg,
    rgba(247, 243, 233, 0.3) 0%,
    rgba(254, 254, 254, 0.5) 50%,
    rgba(247, 243, 233, 0.3) 100%) !important;
}

.client-theme table tbody tr:nth-child(odd),
.client-theme .table-row-odd {
  background: rgba(254, 254, 254, 0.8) !important;
}

.client-theme table tbody tr,
.client-theme .table-row {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border-bottom: 1px solid rgba(201, 169, 110, 0.1) !important;
}

.client-theme table tbody tr:hover,
.client-theme .table-row:hover {
  background: linear-gradient(90deg,
    rgba(201, 169, 110, 0.1) 0%,
    rgba(255, 215, 0, 0.05) 50%,
    rgba(201, 169, 110, 0.1) 100%) !important;
  color: var(--text-luxury) !important;
  transform: scale(1.01) !important;
  box-shadow: 0 4px 20px var(--shadow-luxury) !important;
}

.client-theme table tbody td {
  padding: 16px 24px !important;
  font-family: 'Cairo', sans-serif !important;
  color: var(--text-elegant) !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

/* Override text colors */
.client-theme .text-red-500,
.client-theme .text-red-600 {
  color: #dc2626 !important;
}

.client-theme .text-blue-500,
.client-theme .text-blue-600 {
  color: var(--primary-gold) !important;
}

.client-theme .text-green-500,
.client-theme .text-green-600 {
  color: #059669 !important;
}

/* Override border colors */
.client-theme .border-red-500,
.client-theme .border-red-600 {
  border-color: #dc2626 !important;
}

.client-theme .border-blue-500,
.client-theme .border-blue-600 {
  border-color: var(--primary-gold) !important;
}

.client-theme .border-green-500,
.client-theme .border-green-600 {
  border-color: #059669 !important;
}

/* Override background colors for cards and containers */
.client-theme .bg-white {
  background: var(--accent-white) !important;
}

.client-theme .bg-gray-50,
.client-theme .bg-gray-100 {
  background: var(--accent-cream) !important;
}

.client-theme .bg-gray-200,
.client-theme .bg-gray-300 {
  background: var(--primary-gold-light) !important;
}

/* Override link colors */
.client-theme a {
  color: var(--primary-gold) !important;
  transition: all 0.3s ease;
}

.client-theme a:hover {
  color: var(--primary-gold-dark) !important;
  text-decoration: none;
}

/* Override form elements */
.client-theme input[type="text"],
.client-theme input[type="email"],
.client-theme input[type="password"],
.client-theme input[type="number"],
.client-theme input[type="tel"],
.client-theme input[type="url"],
.client-theme input[type="search"],
.client-theme input[type="date"],
.client-theme input[type="time"],
.client-theme input[type="datetime-local"],
.client-theme textarea,
.client-theme select {
  border: 2px solid var(--primary-gold-light) !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  font-family: 'Cairo', sans-serif !important;
  transition: all 0.3s ease !important;
  background: var(--accent-white) !important;
  color: var(--text-primary) !important;
}

.client-theme input:focus,
.client-theme textarea:focus,
.client-theme select:focus {
  border-color: var(--primary-gold) !important;
  box-shadow: 0 0 0 3px var(--shadow-gold) !important;
  outline: none !important;
  transform: translateY(-1px) !important;
}

/* Override badge and tag colors */
.client-theme .badge,
.client-theme .tag {
  background: var(--gradient-gold) !important;
  color: var(--accent-white) !important;
  border: 1px solid var(--primary-gold) !important;
  font-weight: 600 !important;
  box-shadow: 0 2px 8px var(--shadow-gold) !important;
  border-radius: 20px !important;
  padding: 4px 12px !important;
  font-size: 0.875rem !important;
}

/* Override alert and notification colors */
.client-theme .alert-info,
.client-theme .notification-info {
  background: linear-gradient(135deg, var(--accent-cream), var(--primary-gold-light)) !important;
  border-left: 4px solid var(--primary-gold) !important;
  color: var(--text-primary) !important;
}

/* Override pagination colors */
.client-theme .pagination a,
.client-theme .pagination button {
  background: var(--accent-white) !important;
  color: var(--primary-gold) !important;
  border: 1px solid var(--primary-gold-light) !important;
}

.client-theme .pagination a:hover,
.client-theme .pagination button:hover,
.client-theme .pagination .active {
  background: var(--gradient-gold) !important;
  color: var(--accent-white) !important;
  border-color: var(--primary-gold) !important;
}

/* Override dropdown and menu colors */
.client-theme .dropdown-menu,
.client-theme .menu {
  background: var(--accent-white) !important;
  border: 2px solid var(--primary-gold-light) !important;
  box-shadow: 0 8px 25px var(--shadow-gold) !important;
  border-radius: 8px !important;
}

.client-theme .dropdown-item,
.client-theme .menu-item {
  color: var(--text-primary) !important;
  padding: 8px 16px !important;
  transition: all 0.3s ease !important;
}

.client-theme .dropdown-item:hover,
.client-theme .menu-item:hover {
  background: var(--primary-gold-light) !important;
  color: var(--text-primary) !important;
}

/* Override progress bar colors */
.client-theme .progress-bar {
  background: var(--gradient-gold) !important;
}

.client-theme .progress {
  background: var(--accent-cream) !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

/* Override checkbox and radio colors */
.client-theme input[type="checkbox"]:checked,
.client-theme input[type="radio"]:checked {
  background: var(--primary-gold) !important;
  border-color: var(--primary-gold) !important;
}

/* Override scrollbar colors */
.client-theme ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.client-theme ::-webkit-scrollbar-track {
  background: var(--accent-cream);
  border-radius: 4px;
}

.client-theme ::-webkit-scrollbar-thumb {
  background: var(--primary-gold-light);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.client-theme ::-webkit-scrollbar-thumb:hover {
  background: var(--primary-gold);
}

/* Advanced Luxury Components */

/* Premium Input Fields */
.client-theme input, .client-theme textarea, .client-theme select {
  background: linear-gradient(135deg,
    rgba(254, 254, 254, 0.9) 0%,
    rgba(247, 243, 233, 0.8) 100%) !important;
  border: 2px solid transparent !important;
  border-image: linear-gradient(135deg, rgba(201, 169, 110, 0.3), rgba(255, 215, 0, 0.2)) 1 !important;
  border-radius: 12px !important;
  padding: 16px 20px !important;
  font-family: 'Cairo', sans-serif !important;
  font-weight: 500 !important;
  color: var(--text-luxury) !important;
  backdrop-filter: var(--blur-light) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow:
    0 4px 20px rgba(201, 169, 110, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
}

.client-theme input:focus, .client-theme textarea:focus, .client-theme select:focus {
  border-image: var(--gradient-luxury) 1 !important;
  box-shadow:
    0 8px 30px var(--shadow-luxury),
    0 0 20px rgba(255, 215, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
  outline: none !important;
  transform: translateY(-2px) scale(1.02) !important;
  background: linear-gradient(135deg,
    rgba(254, 254, 254, 0.95) 0%,
    rgba(245, 230, 211, 0.9) 100%) !important;
}

/* Elegant Typography Enhancements */
.client-theme h1, .client-theme h2, .client-theme h3 {
  font-family: 'Amiri', serif !important;
  background: var(--gradient-luxury) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  font-weight: 700 !important;
  position: relative !important;
  margin-bottom: 1.5rem !important;
}

.client-theme h1::after, .client-theme h2::after, .client-theme h3::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 80px;
  height: 4px;
  background: var(--gradient-royal);
  border-radius: 2px;
  animation: shimmerVertical 3s ease-in-out infinite;
}

.client-theme p, .client-theme span, .client-theme div {
  font-family: 'Cairo', sans-serif !important;
  color: var(--text-elegant) !important;
  line-height: 1.8 !important;
}

/* Floating Action Elements */
.luxury-float {
  animation: elegantFloat 4s ease-in-out infinite;
}

.diamond-sparkle {
  animation: diamondSparkle 3s ease-in-out infinite;
}

.royal-glow {
  animation: royalGlow 2s ease-in-out infinite;
}

/* Premium Glass Containers */
.glass-luxury {
  background: var(--glass-bg) !important;
  backdrop-filter: var(--blur-light) !important;
  border: 2px solid var(--glass-border) !important;
  border-radius: 24px !important;
  box-shadow:
    0 20px 60px var(--shadow-luxury),
    inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
}

/* Magnificent Hover Effects */
.hover-luxury {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.hover-luxury:hover {
  transform: translateY(-8px) scale(1.03) !important;
  box-shadow:
    0 25px 70px var(--shadow-luxury),
    0 0 40px rgba(255, 215, 0, 0.3) !important;
}

/* Royal Status Indicators */
.status-royal {
  background: var(--gradient-luxury) !important;
  color: var(--pearl-white) !important;
  padding: 6px 16px !important;
  border-radius: 25px !important;
  font-size: 0.875rem !important;
  font-weight: 700 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  box-shadow: 0 4px 15px var(--shadow-luxury) !important;
  animation: luxuryPulse 3s ease-in-out infinite !important;
}

/* Luxury Dividers */
.divider-luxury {
  height: 2px !important;
  background: var(--gradient-royal) !important;
  border: none !important;
  border-radius: 1px !important;
  margin: 2rem 0 !important;
  position: relative !important;
  overflow: hidden !important;
}

.divider-luxury::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 215, 0, 0.8) 50%,
    transparent 100%);
  animation: goldShimmer 3s ease-in-out infinite;
}

/* Premium Tooltips */
.tooltip-luxury {
  background: var(--gradient-luxury) !important;
  color: var(--pearl-white) !important;
  border-radius: 12px !important;
  padding: 12px 16px !important;
  font-family: 'Cairo', sans-serif !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  box-shadow:
    0 8px 25px var(--shadow-luxury),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  border: 2px solid transparent !important;
  border-image: var(--gradient-royal) 1 !important;
}

/* Responsive Luxury Adjustments */
@media (max-width: 768px) {
  .client-theme .card {
    border-radius: 16px !important;
    margin: 8px !important;
  }

  .client-theme .btn-primary {
    padding: 12px 24px !important;
    font-size: 0.875rem !important;
  }

  .client-theme h1, .client-theme h2, .client-theme h3 {
    font-size: 1.5rem !important;
  }

  .client-theme .topbar {
    padding: 12px 16px !important;
  }

  .client-theme aside {
    border-radius: 0 16px 16px 0 !important;
  }
}

/* Print Luxury Styles */
@media print {
  .client-theme * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  .client-theme .topbar,
  .client-theme aside {
    display: none !important;
  }
}