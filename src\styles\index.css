@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Amiri:wght@400;700&family=Inter:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";
@tailwind base;
@tailwind components;
@tailwind utilities;

html { direction: rtl; }
body {
  direction: rtl;
  background: linear-gradient(135deg, #fefefe 0%, #f9f7f4 100%);
  background-attachment: fixed;
  font-family: 'Cairo', 'Inter', system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue', Arial;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

/* Subtle Background Pattern */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(201, 169, 110, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.02) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* Elegant Wedding Hall Theme - Refined Gold & Cream */
.client-theme {
  /* Refined Gold Palette */
  --primary-gold: #d4af37; /* Classic gold */
  --primary-gold-light: #f4e4a6; /* Light gold */
  --primary-gold-dark: #b8941f; /* Deep gold */
  --accent-gold: #c9a96e; /* Soft gold accent */

  /* Elegant Neutrals */
  --pearl-white: #ffffff; /* Pure white */
  --cream-white: #faf8f3; /* Warm cream */
  --soft-cream: #f7f3e9; /* Soft cream */

  /* Text Colors - Perfect Contrast */
  --text-primary: #2c1810; /* Rich brown for readability */
  --text-secondary: #4a4a4a; /* Medium gray */
  --text-subtle: #6b5b47; /* Subtle brown */

  /* Refined Shadows */
  --shadow-soft: rgba(212, 175, 55, 0.15);
  --shadow-medium: rgba(212, 175, 55, 0.25);
  --shadow-strong: rgba(44, 24, 16, 0.1);

  /* Elegant Gradients */
  --gradient-gold: linear-gradient(135deg, #d4af37 0%, #f4e4a6 100%);
  --gradient-cream: linear-gradient(135deg, #ffffff 0%, #faf8f3 100%);
  --gradient-subtle: linear-gradient(135deg, #faf8f3 0%, #f7f3e9 100%);
}

/* Elegant TopBar */
.client-theme .topbar {
  background: var(--gradient-cream);
  border-bottom: 2px solid var(--primary-gold-light);
  box-shadow: 0 4px 20px var(--shadow-soft);
  backdrop-filter: blur(8px);
}

.client-theme .topbar .text-lg {
  color: var(--text-primary);
  font-family: 'Amiri', serif;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.client-theme .topbar a {
  color: var(--primary-gold);
  font-weight: 600;
  transition: all 0.3s ease;
  padding: 8px 16px;
  border-radius: 8px;
}

.client-theme .topbar a:hover {
  color: var(--primary-gold-dark);
  background: rgba(212, 175, 55, 0.1);
  transform: translateY(-1px);
}

/* Elegant Sidebar */
.client-theme aside {
  background: var(--gradient-cream);
  border-right: 3px solid var(--primary-gold-light);
  box-shadow: 4px 0 20px var(--shadow-soft);
  backdrop-filter: blur(8px);
}

.client-theme aside a {
  color: var(--text-primary);
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 12px;
  margin: 4px 8px;
  padding: 12px 16px;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.client-theme aside a:hover {
  background: var(--gradient-gold);
  color: var(--pearl-white);
  transform: translateX(-4px);
  box-shadow: 0 4px 15px var(--shadow-soft);
}

/* Active link styling */
.client-theme aside a.active {
  background: var(--gradient-gold);
  color: var(--pearl-white);
  border-right: 4px solid var(--primary-gold-dark);
  box-shadow: 0 4px 15px var(--shadow-medium);
  font-weight: 600;
}

/* Elegant Primary Buttons */
.client-theme .btn-primary {
  background: var(--gradient-gold);
  color: var(--pearl-white);
  border: 2px solid var(--primary-gold);
  font-weight: 600;
  font-family: 'Cairo', sans-serif;
  padding: 12px 24px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px var(--shadow-soft);
}

.client-theme .btn-primary:hover {
  background: var(--primary-gold-dark);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow-medium);
}

/* Elegant Cards */
.client-theme .card {
  background: var(--pearl-white);
  border: 2px solid var(--primary-gold-light);
  border-radius: 16px;
  box-shadow: 0 8px 25px var(--shadow-soft);
  transition: all 0.3s ease;
  overflow: hidden;
}

.client-theme .card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 35px var(--shadow-medium);
  border-color: var(--primary-gold);
}

.client-theme .heading-gold {
  color: var(--primary-gold);
  font-family: 'Amiri', serif;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.client-theme .badge-gold {
  background: var(--gradient-gold);
  color: var(--pearl-white);
  border: 1px solid var(--primary-gold);
  font-weight: 600;
  box-shadow: 0 2px 8px var(--shadow-soft);
  border-radius: 20px;
  padding: 4px 12px;
  font-size: 0.875rem;
}

/* Elegant spinner with gold theme */
.client-theme .spinner {
  border-color: var(--primary-gold);
  border-right-color: transparent;
  animation: spin 1s linear infinite;
}

/* Icon circle with gold background */
.client-theme .icon-circle-gold {
  background: var(--gradient-gold);
  color: var(--accent-white);
  box-shadow: 0 4px 15px var(--shadow-gold);
  transition: all 0.3s ease;
}

.client-theme .icon-circle-gold:hover {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 6px 20px var(--shadow-gold);
}

/* Fade-in Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes goldShimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

@keyframes shimmerVertical {
  0%, 100% {
    opacity: 0.3;
    transform: scaleY(1);
  }
  50% {
    opacity: 1;
    transform: scaleY(1.5);
  }
}

@keyframes luxuryPulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(201, 169, 110, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(255, 215, 0, 0.5);
  }
}

@keyframes royalGlow {
  0%, 100% {
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
  }
  50% {
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.8), 0 0 30px rgba(201, 169, 110, 0.6);
  }
}

@keyframes diamondSparkle {
  0%, 100% {
    transform: rotate(0deg) scale(1);
    opacity: 0.7;
  }
  25% {
    transform: rotate(90deg) scale(1.1);
    opacity: 1;
  }
  50% {
    transform: rotate(180deg) scale(0.9);
    opacity: 0.8;
  }
  75% {
    transform: rotate(270deg) scale(1.1);
    opacity: 1;
  }
}

@keyframes elegantFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-8px) rotate(1deg);
  }
  66% {
    transform: translateY(-4px) rotate(-1deg);
  }
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

.gold-shimmer {
  background: linear-gradient(90deg, var(--primary-gold) 0%, var(--primary-gold-light) 50%, var(--primary-gold) 100%);
  background-size: 200% 100%;
  animation: goldShimmer 2s ease-in-out infinite;
}

/* Typography improvements */
.client-theme h1, .client-theme h2, .client-theme h3 {
  font-family: 'Amiri', serif;
  color: var(--text-primary);
  font-weight: 700;
}

.client-theme p, .client-theme span, .client-theme div {
  font-family: 'Cairo', sans-serif;
  color: var(--text-secondary);
}

/* Parallax effect container */
.parallax-container {
  position: relative;
  overflow: hidden;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.parallax-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(255, 255, 255, 0.9) 100%);
  backdrop-filter: blur(2px);
}

/* Enhanced form inputs */
.client-theme input, .client-theme textarea, .client-theme select {
  border: 2px solid var(--primary-gold-light);
  border-radius: 8px;
  padding: 12px 16px;
  font-family: 'Cairo', sans-serif;
  transition: all 0.3s ease;
  background: var(--pearl-white);
}

.client-theme input:focus, .client-theme textarea:focus, .client-theme select:focus {
  border-color: var(--primary-gold);
  box-shadow: 0 0 0 3px var(--shadow-soft);
  outline: none;
  transform: translateY(-1px);
}

/* Elegant table styling */
.client-theme table {
  background: var(--accent-white);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 25px var(--shadow-gold);
}

.client-theme th {
  background: var(--gradient-gold);
  color: var(--accent-white);
  font-family: 'Amiri', serif;
  font-weight: 700;
  padding: 16px;
}

.client-theme td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--primary-gold-light);
  font-family: 'Cairo', sans-serif;
  color: var(--text-secondary);
}

.client-theme tr:hover {
  background: var(--accent-cream);
  transition: all 0.3s ease;
}

/* Advanced visual effects */
.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(212, 175, 55, 0.2);
  box-shadow: 0 8px 32px rgba(212, 175, 55, 0.1);
}

.gold-gradient-text {
  background: var(--gradient-gold);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* Floating elements */
.floating {
  animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Elegant shadows */
.shadow-gold-lg {
  box-shadow: 0 20px 40px var(--shadow-gold);
}

.shadow-gold-xl {
  box-shadow: 0 25px 50px var(--shadow-gold);
}

/* Responsive design improvements */
@media (max-width: 768px) {
  .client-theme .topbar {
    padding: 8px 16px;
  }

  .client-theme aside {
    width: 100%;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .client-theme aside.open {
    transform: translateX(0);
  }

  .client-theme .card {
    margin: 8px;
    border-radius: 8px;
  }
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Status indicators */
.status-success {
  background: linear-gradient(135deg, #10b981, #34d399);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.status-warning {
  background: linear-gradient(135deg, #f59e0b, #fbbf24);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.status-error {
  background: linear-gradient(135deg, #ef4444, #f87171);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

/* Enhanced buttons */
.btn-gold {
  background: var(--gradient-gold);
  color: var(--accent-white);
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-family: 'Cairo', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px var(--shadow-gold);
}

.btn-gold:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow-gold);
  background: var(--primary-gold-dark);
}

.btn-outline-gold {
  background: transparent;
  color: var(--primary-gold);
  border: 2px solid var(--primary-gold);
  padding: 10px 22px;
  border-radius: 8px;
  font-weight: 600;
  font-family: 'Cairo', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-outline-gold:hover {
  background: var(--gradient-gold);
  color: var(--accent-white);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px var(--shadow-gold);
}

/* Modal and overlay improvements */
.modal-overlay {
  background: rgba(44, 24, 16, 0.7);
  backdrop-filter: blur(5px);
}

.modal-content {
  background: var(--accent-white);
  border-radius: 16px;
  box-shadow: 0 25px 50px rgba(44, 24, 16, 0.3);
  border: 2px solid var(--primary-gold-light);
}

/* Notification styles */
.notification {
  border-radius: 12px;
  padding: 16px 20px;
  margin: 8px 0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  animation: slideInRight 0.3s ease-out;
}

.notification-success {
  background: linear-gradient(135deg, #ecfdf5, #d1fae5);
  border-left: 4px solid #10b981;
  color: #065f46;
}

.notification-warning {
  background: linear-gradient(135deg, #fffbeb, #fef3c7);
  border-left: 4px solid #f59e0b;
  color: #92400e;
}

.notification-error {
  background: linear-gradient(135deg, #fef2f2, #fecaca);
  border-left: 4px solid #ef4444;
  color: #991b1b;
}

/* Arabic text enhancements */
.arabic-text {
  font-family: 'Amiri', serif;
  line-height: 1.8;
  letter-spacing: 0.5px;
}

.english-text {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
}

/* Print styles */
@media print {
  .client-theme {
    background: white !important;
  }

  .client-theme .topbar,
  .client-theme aside {
    display: none !important;
  }

  .client-theme .card {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}

/* Override all default button colors to match gold theme */
.client-theme button {
  background: var(--gradient-gold);
  color: var(--pearl-white);
  border: 2px solid var(--primary-gold);
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px var(--shadow-soft);
  border-radius: 8px;
  padding: 10px 16px;
  font-family: 'Cairo', sans-serif;
}

.client-theme button:hover {
  background: var(--primary-gold-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px var(--shadow-medium);
}

/* Override specific button types */
.client-theme button.bg-red-500,
.client-theme button.bg-red-600,
.client-theme .bg-red-500,
.client-theme .bg-red-600 {
  background: #dc2626 !important;
  border-color: #dc2626 !important;
  color: white !important;
}

.client-theme button.bg-red-500:hover,
.client-theme button.bg-red-600:hover,
.client-theme .bg-red-500:hover,
.client-theme .bg-red-600:hover {
  background: #b91c1c !important;
}

.client-theme button.bg-blue-500,
.client-theme button.bg-blue-600,
.client-theme .bg-blue-500,
.client-theme .bg-blue-600 {
  background: var(--gradient-gold) !important;
  border-color: var(--primary-gold) !important;
  color: var(--pearl-white) !important;
}

.client-theme button.bg-blue-500:hover,
.client-theme button.bg-blue-600:hover,
.client-theme .bg-blue-500:hover,
.client-theme .bg-blue-600:hover {
  background: var(--primary-gold-dark) !important;
}

.client-theme button.bg-green-500,
.client-theme button.bg-green-600,
.client-theme .bg-green-500,
.client-theme .bg-green-600 {
  background: #059669 !important;
  border-color: #059669 !important;
  color: white !important;
}

.client-theme button.bg-green-500:hover,
.client-theme button.bg-green-600:hover,
.client-theme .bg-green-500:hover,
.client-theme .bg-green-600:hover {
  background: #047857 !important;
}

/* Elegant Table Headers */
.client-theme table thead th,
.client-theme .table-header,
.client-theme th {
  background: var(--gradient-gold) !important;
  color: var(--pearl-white) !important;
  font-family: 'Amiri', serif !important;
  font-weight: 700 !important;
  padding: 16px 20px !important;
  border: none !important;
  text-shadow: 0 1px 2px rgba(0,0,0,0.2) !important;
}

/* Elegant Table Rows */
.client-theme table tbody tr:nth-child(even),
.client-theme .table-row-even {
  background: var(--soft-cream) !important;
}

.client-theme table tbody tr:nth-child(odd),
.client-theme .table-row-odd {
  background: var(--pearl-white) !important;
}

.client-theme table tbody tr,
.client-theme .table-row {
  transition: all 0.3s ease !important;
  border-bottom: 1px solid var(--primary-gold-light) !important;
}

.client-theme table tbody tr:hover,
.client-theme .table-row:hover {
  background: var(--cream-white) !important;
  color: var(--text-primary) !important;
  box-shadow: 0 2px 8px var(--shadow-soft) !important;
}

.client-theme table tbody td {
  padding: 12px 16px !important;
  font-family: 'Cairo', sans-serif !important;
  color: var(--text-primary) !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

/* Override text colors */
.client-theme .text-red-500,
.client-theme .text-red-600 {
  color: #dc2626 !important;
}

.client-theme .text-blue-500,
.client-theme .text-blue-600 {
  color: var(--primary-gold) !important;
}

.client-theme .text-green-500,
.client-theme .text-green-600 {
  color: #059669 !important;
}

/* Override border colors */
.client-theme .border-red-500,
.client-theme .border-red-600 {
  border-color: #dc2626 !important;
}

.client-theme .border-blue-500,
.client-theme .border-blue-600 {
  border-color: var(--primary-gold) !important;
}

.client-theme .border-green-500,
.client-theme .border-green-600 {
  border-color: #059669 !important;
}

/* Override background colors for cards and containers */
.client-theme .bg-white {
  background: var(--accent-white) !important;
}

.client-theme .bg-gray-50,
.client-theme .bg-gray-100 {
  background: var(--accent-cream) !important;
}

.client-theme .bg-gray-200,
.client-theme .bg-gray-300 {
  background: var(--primary-gold-light) !important;
}

/* Override link colors */
.client-theme a {
  color: var(--primary-gold) !important;
  transition: all 0.3s ease;
}

.client-theme a:hover {
  color: var(--primary-gold-dark) !important;
  text-decoration: none;
}

/* Override form elements */
.client-theme input[type="text"],
.client-theme input[type="email"],
.client-theme input[type="password"],
.client-theme input[type="number"],
.client-theme input[type="tel"],
.client-theme input[type="url"],
.client-theme input[type="search"],
.client-theme input[type="date"],
.client-theme input[type="time"],
.client-theme input[type="datetime-local"],
.client-theme textarea,
.client-theme select {
  border: 2px solid var(--primary-gold-light) !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  font-family: 'Cairo', sans-serif !important;
  transition: all 0.3s ease !important;
  background: var(--accent-white) !important;
  color: var(--text-primary) !important;
}

.client-theme input:focus,
.client-theme textarea:focus,
.client-theme select:focus {
  border-color: var(--primary-gold) !important;
  box-shadow: 0 0 0 3px var(--shadow-gold) !important;
  outline: none !important;
  transform: translateY(-1px) !important;
}

/* Override badge and tag colors */
.client-theme .badge,
.client-theme .tag {
  background: var(--gradient-gold) !important;
  color: var(--accent-white) !important;
  border: 1px solid var(--primary-gold) !important;
  font-weight: 600 !important;
  box-shadow: 0 2px 8px var(--shadow-gold) !important;
  border-radius: 20px !important;
  padding: 4px 12px !important;
  font-size: 0.875rem !important;
}

/* Override alert and notification colors */
.client-theme .alert-info,
.client-theme .notification-info {
  background: linear-gradient(135deg, var(--accent-cream), var(--primary-gold-light)) !important;
  border-left: 4px solid var(--primary-gold) !important;
  color: var(--text-primary) !important;
}

/* Override pagination colors */
.client-theme .pagination a,
.client-theme .pagination button {
  background: var(--accent-white) !important;
  color: var(--primary-gold) !important;
  border: 1px solid var(--primary-gold-light) !important;
}

.client-theme .pagination a:hover,
.client-theme .pagination button:hover,
.client-theme .pagination .active {
  background: var(--gradient-gold) !important;
  color: var(--accent-white) !important;
  border-color: var(--primary-gold) !important;
}

/* Override dropdown and menu colors */
.client-theme .dropdown-menu,
.client-theme .menu {
  background: var(--accent-white) !important;
  border: 2px solid var(--primary-gold-light) !important;
  box-shadow: 0 8px 25px var(--shadow-gold) !important;
  border-radius: 8px !important;
}

.client-theme .dropdown-item,
.client-theme .menu-item {
  color: var(--text-primary) !important;
  padding: 8px 16px !important;
  transition: all 0.3s ease !important;
}

.client-theme .dropdown-item:hover,
.client-theme .menu-item:hover {
  background: var(--primary-gold-light) !important;
  color: var(--text-primary) !important;
}

/* Override progress bar colors */
.client-theme .progress-bar {
  background: var(--gradient-gold) !important;
}

.client-theme .progress {
  background: var(--accent-cream) !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

/* Override checkbox and radio colors */
.client-theme input[type="checkbox"]:checked,
.client-theme input[type="radio"]:checked {
  background: var(--primary-gold) !important;
  border-color: var(--primary-gold) !important;
}

/* Override scrollbar colors */
.client-theme ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.client-theme ::-webkit-scrollbar-track {
  background: var(--accent-cream);
  border-radius: 4px;
}

.client-theme ::-webkit-scrollbar-thumb {
  background: var(--primary-gold-light);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.client-theme ::-webkit-scrollbar-thumb:hover {
  background: var(--primary-gold);
}

/* Advanced Luxury Components */

/* Premium Input Fields */
.client-theme input, .client-theme textarea, .client-theme select {
  background: linear-gradient(135deg,
    rgba(254, 254, 254, 0.9) 0%,
    rgba(247, 243, 233, 0.8) 100%) !important;
  border: 2px solid transparent !important;
  border-image: linear-gradient(135deg, rgba(201, 169, 110, 0.3), rgba(255, 215, 0, 0.2)) 1 !important;
  border-radius: 12px !important;
  padding: 16px 20px !important;
  font-family: 'Cairo', sans-serif !important;
  font-weight: 500 !important;
  color: var(--text-luxury) !important;
  backdrop-filter: var(--blur-light) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow:
    0 4px 20px rgba(201, 169, 110, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
}

.client-theme input:focus, .client-theme textarea:focus, .client-theme select:focus {
  border-image: var(--gradient-luxury) 1 !important;
  box-shadow:
    0 8px 30px var(--shadow-luxury),
    0 0 20px rgba(255, 215, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
  outline: none !important;
  transform: translateY(-2px) scale(1.02) !important;
  background: linear-gradient(135deg,
    rgba(254, 254, 254, 0.95) 0%,
    rgba(245, 230, 211, 0.9) 100%) !important;
}

/* Elegant Typography */
.client-theme h1, .client-theme h2, .client-theme h3 {
  font-family: 'Amiri', serif !important;
  color: var(--text-primary) !important;
  font-weight: 700 !important;
  margin-bottom: 1.5rem !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
}

.client-theme p, .client-theme span, .client-theme div {
  font-family: 'Cairo', sans-serif !important;
  color: var(--text-primary-dark) !important;
  line-height: 1.8 !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
}

/* Floating Action Elements */
.luxury-float {
  animation: elegantFloat 4s ease-in-out infinite;
}

.diamond-sparkle {
  animation: diamondSparkle 3s ease-in-out infinite;
}

.royal-glow {
  animation: royalGlow 2s ease-in-out infinite;
}

/* Premium Glass Containers */
.glass-luxury {
  background: var(--glass-bg) !important;
  backdrop-filter: var(--blur-light) !important;
  border: 2px solid var(--glass-border) !important;
  border-radius: 24px !important;
  box-shadow:
    0 20px 60px var(--shadow-luxury),
    inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
}

/* Magnificent Hover Effects */
.hover-luxury {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.hover-luxury:hover {
  transform: translateY(-8px) scale(1.03) !important;
  box-shadow:
    0 25px 70px var(--shadow-luxury),
    0 0 40px rgba(255, 215, 0, 0.3) !important;
}

/* Royal Status Indicators */
.status-royal {
  background: var(--gradient-luxury) !important;
  color: var(--pearl-white) !important;
  padding: 6px 16px !important;
  border-radius: 25px !important;
  font-size: 0.875rem !important;
  font-weight: 700 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  box-shadow: 0 4px 15px var(--shadow-luxury) !important;
  animation: luxuryPulse 3s ease-in-out infinite !important;
}

/* Luxury Dividers */
.divider-luxury {
  height: 2px !important;
  background: var(--gradient-royal) !important;
  border: none !important;
  border-radius: 1px !important;
  margin: 2rem 0 !important;
  position: relative !important;
  overflow: hidden !important;
}

.divider-luxury::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 215, 0, 0.8) 50%,
    transparent 100%);
  animation: goldShimmer 3s ease-in-out infinite;
}

/* Premium Tooltips */
.tooltip-luxury {
  background: var(--gradient-luxury) !important;
  color: var(--pearl-white) !important;
  border-radius: 12px !important;
  padding: 12px 16px !important;
  font-family: 'Cairo', sans-serif !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  box-shadow:
    0 8px 25px var(--shadow-luxury),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  border: 2px solid transparent !important;
  border-image: var(--gradient-royal) 1 !important;
}

/* Responsive Luxury Adjustments */
@media (max-width: 768px) {
  .client-theme .card {
    border-radius: 16px !important;
    margin: 8px !important;
  }

  .client-theme .btn-primary {
    padding: 12px 24px !important;
    font-size: 0.875rem !important;
  }

  .client-theme h1, .client-theme h2, .client-theme h3 {
    font-size: 1.5rem !important;
  }

  .client-theme .topbar {
    padding: 12px 16px !important;
  }

  .client-theme aside {
    border-radius: 0 16px 16px 0 !important;
  }
}

/* Print Luxury Styles */
@media print {
  .client-theme * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  .client-theme .topbar,
  .client-theme aside {
    display: none !important;
  }
}

/* Enhanced Text Readability - Global Overrides */
.client-theme * {
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

/* Ensure all text elements have proper contrast */
.client-theme label,
.client-theme .form-label,
.client-theme .input-label {
  color: var(--text-primary-dark) !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
}

.client-theme .text-sm,
.client-theme .text-xs {
  color: var(--text-elegant) !important;
  font-weight: 500 !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
}

.client-theme .text-lg,
.client-theme .text-xl,
.client-theme .text-2xl {
  color: var(--text-primary-dark) !important;
  font-weight: 700 !important;
  text-shadow: 0 2px 4px rgba(255, 255, 255, 0.8) !important;
}

/* Enhanced contrast for links */
.client-theme a {
  color: var(--primary-gold-dark) !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
}

.client-theme a:hover {
  color: var(--text-primary-dark) !important;
  text-shadow: 0 2px 4px rgba(255, 255, 255, 0.9) !important;
}

/* Better contrast for form elements */
.client-theme input::placeholder,
.client-theme textarea::placeholder {
  color: var(--text-subtle) !important;
  opacity: 0.8 !important;
}

/* Enhanced readability for status indicators */
.client-theme .badge,
.client-theme .tag,
.client-theme .status {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  font-weight: 700 !important;
}

/* Improved sidebar text contrast */
.client-theme aside a:hover {
  color: var(--text-primary-dark) !important;
  text-shadow: 0 2px 4px rgba(255, 255, 255, 0.9) !important;
}

/* Better topbar text visibility */
.client-theme .topbar .text-lg {
  color: var(--text-primary-dark) !important;
  text-shadow: 0 2px 4px rgba(255, 255, 255, 0.8) !important;
}

.client-theme .topbar a {
  color: var(--primary-gold-dark) !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
}

/* Enhanced table text readability */
.client-theme table tbody tr:hover td {
  color: var(--text-primary-dark) !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.9) !important;
}

/* Improved card content readability */
.client-theme .card h1,
.client-theme .card h2,
.client-theme .card h3,
.client-theme .card h4,
.client-theme .card h5,
.client-theme .card h6 {
  color: var(--text-primary-dark) !important;
  text-shadow: 0 2px 4px rgba(255, 255, 255, 0.8) !important;
}

.client-theme .card p,
.client-theme .card span,
.client-theme .card div {
  color: var(--text-primary-dark) !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
}

/* 🌟 REFINED ELEGANT ENHANCEMENTS 🌟 */

/* Subtle 3D Effect */
.elegant-3d {
  transition: transform 0.3s ease;
}

.elegant-3d:hover {
  transform: translateY(-2px) scale(1.02);
}

/* Elegant Gold Text Effect */
.gold-text {
  background: var(--gradient-gold);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* Elegant Hover Buttons */
.elegant-button {
  transition: all 0.3s ease;
  border-radius: 8px;
}

.elegant-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow-medium);
}

/* Liquid Loading Animation */
.liquid-loader {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: var(--gradient-luxury);
  overflow: hidden;
}

.liquid-loader::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80%;
  height: 80%;
  background: radial-gradient(circle,
    rgba(255, 255, 255, 0.8) 0%,
    transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: liquidPulse 2s ease-in-out infinite;
}

@keyframes liquidPulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.8; }
  50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.4; }
}

/* Magnetic Hover Effect */
.magnetic {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.magnetic:hover {
  transform: translate3d(0, -8px, 0) scale(1.02);
}

/* Neon Glow Effect */
.neon-glow {
  text-shadow:
    0 0 5px rgba(255, 215, 0, 0.8),
    0 0 10px rgba(255, 215, 0, 0.6),
    0 0 15px rgba(255, 215, 0, 0.4),
    0 0 20px rgba(201, 169, 110, 0.3);
  animation: neonFlicker 2s ease-in-out infinite alternate;
}

@keyframes neonFlicker {
  0%, 100% {
    text-shadow:
      0 0 5px rgba(255, 215, 0, 0.8),
      0 0 10px rgba(255, 215, 0, 0.6),
      0 0 15px rgba(255, 215, 0, 0.4),
      0 0 20px rgba(201, 169, 110, 0.3);
  }
  50% {
    text-shadow:
      0 0 2px rgba(255, 215, 0, 0.9),
      0 0 5px rgba(255, 215, 0, 0.7),
      0 0 8px rgba(255, 215, 0, 0.5),
      0 0 12px rgba(201, 169, 110, 0.4);
  }
}

/* Crystalline Cards */
.crystal-card {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  position: relative;
  overflow: hidden;
}

.crystal-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    from 0deg,
    transparent 0deg,
    rgba(255, 215, 0, 0.1) 60deg,
    transparent 120deg,
    rgba(201, 169, 110, 0.1) 180deg,
    transparent 240deg,
    rgba(255, 215, 0, 0.1) 300deg,
    transparent 360deg
  );
  animation: crystalRotate 10s linear infinite;
  opacity: 0.7;
}

@keyframes crystalRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Ripple Effect */
.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 215, 0, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple:hover::after {
  width: 300px;
  height: 300px;
}

/* Gradient Border Animation */
.gradient-border {
  position: relative;
  background: var(--gradient-luxury);
  border-radius: 16px;
  padding: 2px;
}

.gradient-border::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: var(--gradient-royal);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  animation: gradientRotate 3s linear infinite;
}

@keyframes gradientRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Floating Elements */
.float-1 { animation: float1 6s ease-in-out infinite; }
.float-2 { animation: float2 8s ease-in-out infinite; }
.float-3 { animation: float3 10s ease-in-out infinite; }

@keyframes float1 {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

@keyframes float2 {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(-3deg); }
}

@keyframes float3 {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-25px) rotate(7deg); }
}

/* Parallax Scroll Effect */
.parallax-element {
  transform: translateZ(0);
  will-change: transform;
}

/* Advanced Glassmorphism */
.ultra-glass {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(25px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow:
    0 25px 45px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 -1px 0 rgba(0, 0, 0, 0.05);
}

/* Luxury Cursor Effects */
.luxury-cursor {
  cursor: none;
}

.custom-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  background: var(--gradient-luxury);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  transition: transform 0.1s ease;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.custom-cursor.hover {
  transform: scale(1.5);
  background: var(--gradient-royal);
}

/* Breathing Animation */
.breathing {
  animation: breathe 4s ease-in-out infinite;
}

@keyframes breathe {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}
/* 🚀 NEXT-LEVEL INTERACTIVE EFFECTS 🚀 */

/* Tilt Effect on Hover */
.tilt-effect {
  transition: transform 0.3s ease;
}

.tilt-effect:hover {
  transform: perspective(1000px) rotateX(10deg) rotateY(10deg) scale3d(1.05, 1.05, 1.05);
}

/* Glitch Text Effect */
.glitch-text {
  position: relative;
  color: var(--primary-gold);
  font-weight: bold;
}

.glitch-text::before,
.glitch-text::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch-text::before {
  animation: glitch-1 0.5s infinite;
  color: #ff00ff;
  z-index: -1;
}

.glitch-text::after {
  animation: glitch-2 0.5s infinite;
  color: #00ffff;
  z-index: -2;
}

@keyframes glitch-1 {
  0%, 14%, 15%, 49%, 50%, 99%, 100% { transform: translate(0); }
  15%, 49% { transform: translate(-2px, 1px); }
}

@keyframes glitch-2 {
  0%, 20%, 21%, 62%, 63%, 99%, 100% { transform: translate(0); }
  21%, 62% { transform: translate(2px, -1px); }
}

/* Typewriter Effect */
.typewriter {
  overflow: hidden;
  border-right: 3px solid var(--primary-gold);
  white-space: nowrap;
  margin: 0 auto;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: var(--primary-gold); }
}

/* Staggered Animation */
.stagger-item {
  opacity: 0;
  transform: translateY(30px);
  animation: staggerIn 0.6s ease forwards;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes staggerIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Pulse Ring Effect */
.pulse-ring {
  position: relative;
}

.pulse-ring::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border: 2px solid var(--primary-gold);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulseRing 2s ease-out infinite;
}

@keyframes pulseRing {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

/* Slide Reveal Effect */
.slide-reveal {
  position: relative;
  overflow: hidden;
}

.slide-reveal::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-luxury);
  transition: left 0.8s cubic-bezier(0.77, 0, 0.175, 1);
}

.slide-reveal:hover::before {
  left: 100%;
}

/* Bounce In Effect */
.bounce-in {
  animation: bounceIn 1s ease;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Zoom Blur Effect */
.zoom-blur {
  transition: all 0.3s ease;
}

.zoom-blur:hover {
  transform: scale(1.1);
  filter: blur(1px);
}

/* Text Gradient Animation */
.gradient-text-animated {
  background: linear-gradient(-45deg, #ff6b35, #f7931e, #ffd700, #c9a96e);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientText 4s ease infinite;
}

@keyframes gradientText {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Flip Card Effect */
.flip-card {
  background-color: transparent;
  perspective: 1000px;
}

.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.8s;
  transform-style: preserve-3d;
}

.flip-card:hover .flip-card-inner {
  transform: rotateY(180deg);
}

.flip-card-front, .flip-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  border-radius: 16px;
  box-shadow: 0 8px 25px var(--shadow-luxury);
}

.flip-card-back {
  background: var(--gradient-luxury);
  color: var(--pearl-white);
  transform: rotateY(180deg);
}

/* Elastic Scale */
.elastic-scale {
  transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.elastic-scale:hover {
  transform: scale(1.1);
}

/* Shake Animation */
.shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* Glow Pulse */
.glow-pulse {
  animation: glowPulse 2s ease-in-out infinite alternate;
}

@keyframes glowPulse {
  from {
    box-shadow: 0 0 20px var(--shadow-luxury);
  }
  to {
    box-shadow: 0 0 40px var(--shadow-glow), 0 0 60px var(--shadow-luxury);
  }
}

/* Rotate Scale */
.rotate-scale {
  transition: transform 0.3s ease;
}

.rotate-scale:hover {
  transform: rotate(5deg) scale(1.05);
}

/* Slide Up */
.slide-up {
  transform: translateY(100px);
  opacity: 0;
  animation: slideUp 0.8s ease forwards;
}

@keyframes slideUp {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Wobble Effect */
.wobble {
  animation: wobble 1s ease-in-out;
}

@keyframes wobble {
  0% { transform: translateX(0%); }
  15% { transform: translateX(-25px) rotate(-5deg); }
  30% { transform: translateX(20px) rotate(3deg); }
  45% { transform: translateX(-15px) rotate(-3deg); }
  60% { transform: translateX(10px) rotate(2deg); }
  75% { transform: translateX(-5px) rotate(-1deg); }
  100% { transform: translateX(0%); }
}

/* Advanced Hover States */
.luxury-hover {
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.luxury-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.luxury-hover:hover::before {
  left: 100%;
}

.luxury-hover:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow:
    0 20px 40px var(--shadow-luxury),
    0 0 30px rgba(255, 215, 0, 0.3);
}

/* Infinite Rotation */
.infinite-rotate {
  animation: infiniteRotate 20s linear infinite;
}

@keyframes infiniteRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Fade In Directions */
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.6s ease forwards;
}

.fade-in-down {
  opacity: 0;
  transform: translateY(-30px);
  animation: fadeInDown 0.6s ease forwards;
}

.fade-in-left {
  opacity: 0;
  transform: translateX(-30px);
  animation: fadeInLeft 0.6s ease forwards;
}

.fade-in-right {
  opacity: 0;
  transform: translateX(30px);
  animation: fadeInRight 0.6s ease forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
/* 🎭 ELEGANT VISUAL EFFECTS 🎭 */

/* Simple Fade Animation */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Simple Glow Effect */
.soft-glow {
  text-shadow: 0 0 10px var(--primary-gold);
}

/* Elegant Hover Effects */
.elegant-hover {
  transition: all 0.3s ease;
}

.elegant-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px var(--shadow-soft);
}

/* Clean and Simple Effects Only */

/* Luxury Loader */
.luxury-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.loader-text {
  font-family: 'Amiri', serif;
  font-size: 24px;
  font-weight: 700;
}

/* Advanced Button Morphing */
.morph-advanced {
  position: relative;
  background: var(--gradient-luxury);
  border: none;
  border-radius: 50px;
  padding: 15px 30px;
  color: var(--pearl-white);
  font-weight: 700;
  overflow: hidden;
  transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
}

.morph-advanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent);
  transition: left 0.6s;
}

.morph-advanced::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.6s ease;
}

.morph-advanced:hover {
  border-radius: 15px;
  transform: scale(1.05) translateY(-3px);
  box-shadow:
    0 20px 40px rgba(201, 169, 110, 0.4),
    0 0 30px rgba(255, 215, 0, 0.3);
}

.morph-advanced:hover::before {
  left: 100%;
}

.morph-advanced:hover::after {
  width: 300px;
  height: 300px;
}

/* Crystalline Structure */
.crystalline {
  position: relative;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  clip-path: polygon(20% 0%, 80% 0%, 100% 20%, 100% 80%, 80% 100%, 20% 100%, 0% 80%, 0% 20%);
  transition: all 0.3s ease;
}

.crystalline:hover {
  clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%);
  transform: scale(1.05);
}

/* Plasma Effect */
.plasma-effect {
  background: linear-gradient(45deg, #ff6b35, #f7931e, #ffd700, #c9a96e, #667eea, #764ba2);
  background-size: 400% 400%;
  animation: plasmaFlow 8s ease infinite;
  filter: blur(1px);
}

@keyframes plasmaFlow {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 100% 100%; }
  75% { background-position: 50% 100%; }
  100% { background-position: 0% 50%; }
}

/* Elegant Scrollbar */
.client-theme ::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

.client-theme ::-webkit-scrollbar-track {
  background: var(--cream-white);
  border-radius: 8px;
}

.client-theme ::-webkit-scrollbar-thumb {
  background: var(--gradient-gold);
  border-radius: 8px;
  border: 2px solid var(--cream-white);
  transition: all 0.3s ease;
}

.client-theme ::-webkit-scrollbar-thumb:hover {
  background: var(--primary-gold-dark);
}

.client-theme ::-webkit-scrollbar-corner {
  background: var(--cream-white);
}

/* Ultimate Performance Optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Responsive Luxury Enhancements */
@media (max-width: 768px) {
  .holographic-text,
  .liquid-metal,
  .prism-effect {
    font-size: 1.2rem;
  }

  .luxury-3d:hover {
    transform: rotateY(2deg) rotateX(1deg) translateZ(10px);
  }

  .morph-advanced {
    padding: 12px 24px;
    font-size: 0.9rem;
  }

  .crystalline {
    clip-path: polygon(10% 0%, 90% 0%, 100% 10%, 100% 90%, 90% 100%, 10% 100%, 0% 90%, 0% 10%);
  }
}

/* Print Optimizations */
@media print {
  .holographic-text,
  .liquid-metal,
  .prism-effect,
  .cyberpunk-glow,
  .neon-glow {
    -webkit-text-fill-color: initial !important;
    background: none !important;
    color: #000 !important;
    text-shadow: none !important;
  }

  .luxury-3d,
  .tilt-effect,
  .magnetic,
  .morph-advanced {
    transform: none !important;
    transition: none !important;
  }
}