@tailwind base;
@tailwind components;
@tailwind utilities;
@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Amiri:wght@400;700&family=Inter:wght@300;400;500;600;700&display=swap');

html { direction: rtl; }
body {
  direction: rtl;
  @apply bg-gradient-to-br from-amber-50 to-white;
  font-family: 'Cairo', 'Inter', system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue', Arial;
  min-height: 100vh;
}

/* Elegant Wedding Hall Theme - Gold, White, Dark accents */
.client-theme {
  --primary-gold: #d4af37; /* Rich gold */
  --primary-gold-light: #f4e4a6; /* Light gold */
  --primary-gold-dark: #b8941f; /* Dark gold */
  --accent-white: #ffffff; /* Pure white */
  --accent-cream: #faf8f3; /* Cream white */
  --accent-dark: #2c1810; /* Dark brown */
  --accent-black: #1a1a1a; /* Elegant black */
  --text-primary: #2c1810; /* Dark brown for text */
  --text-secondary: #6b5b47; /* Medium brown */
  --shadow-gold: rgba(212, 175, 55, 0.15); /* Gold shadow */
  --gradient-gold: linear-gradient(135deg, #d4af37 0%, #f4e4a6 100%);
}

.client-theme .topbar {
  background: linear-gradient(90deg, var(--accent-white), var(--accent-cream));
  border-bottom: 3px solid var(--primary-gold);
  box-shadow: 0 4px 20px var(--shadow-gold);
  backdrop-filter: blur(10px);
}

.client-theme .topbar .text-lg {
  color: var(--text-primary);
  font-family: 'Amiri', serif;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.client-theme .topbar a {
  color: var(--primary-gold);
  font-weight: 600;
  transition: all 0.3s ease;
}
.client-theme .topbar a:hover {
  color: var(--primary-gold-dark);
  transform: translateY(-1px);
  text-shadow: 0 2px 8px var(--shadow-gold);
}

.client-theme aside {
  background: linear-gradient(180deg, var(--accent-white) 0%, var(--accent-cream) 100%);
  border-right: 3px solid var(--primary-gold);
  box-shadow: 4px 0 20px var(--shadow-gold);
  backdrop-filter: blur(10px);
}
.client-theme aside a {
  color: var(--text-primary);
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 4px 8px;
}
.client-theme aside a:hover {
  background: var(--gradient-gold);
  color: var(--accent-white);
  transform: translateX(-4px);
  box-shadow: 0 4px 15px var(--shadow-gold);
}

/* Active link in client sidebar */
.client-theme aside a.active {
  background: var(--gradient-gold);
  color: var(--accent-white);
  border-right: 4px solid var(--primary-gold-dark);
  box-shadow: 0 4px 15px var(--shadow-gold);
  font-weight: 600;
}

.client-theme .btn-primary {
  background: var(--gradient-gold);
  color: var(--accent-white);
  border: 2px solid var(--primary-gold);
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px var(--shadow-gold);
}
.client-theme .btn-primary:hover {
  background: var(--primary-gold-dark);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow-gold);
}

.client-theme .card {
  background: var(--accent-white);
  border: 2px solid var(--primary-gold-light);
  border-radius: 1rem;
  box-shadow: 0 10px 30px var(--shadow-gold);
  transition: all 0.3s ease;
  overflow: hidden;
}

.client-theme .card:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 40px var(--shadow-gold);
  border-color: var(--primary-gold);
}

.client-theme .heading-gold {
  color: var(--primary-gold);
  font-family: 'Amiri', serif;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.client-theme .badge-gold {
  background: var(--gradient-gold);
  color: var(--accent-white);
  border: 1px solid var(--primary-gold);
  font-weight: 600;
  box-shadow: 0 2px 8px var(--shadow-gold);
}

/* Elegant spinner with gold theme */
.client-theme .spinner {
  border-color: var(--primary-gold);
  border-right-color: transparent;
  animation: spin 1s linear infinite;
}

/* Icon circle with gold background */
.client-theme .icon-circle-gold {
  background: var(--gradient-gold);
  color: var(--accent-white);
  box-shadow: 0 4px 15px var(--shadow-gold);
  transition: all 0.3s ease;
}

.client-theme .icon-circle-gold:hover {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 6px 20px var(--shadow-gold);
}

/* Fade-in Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes goldShimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

.gold-shimmer {
  background: linear-gradient(90deg, var(--primary-gold) 0%, var(--primary-gold-light) 50%, var(--primary-gold) 100%);
  background-size: 200% 100%;
  animation: goldShimmer 2s ease-in-out infinite;
}

/* Typography improvements */
.client-theme h1, .client-theme h2, .client-theme h3 {
  font-family: 'Amiri', serif;
  color: var(--text-primary);
  font-weight: 700;
}

.client-theme p, .client-theme span, .client-theme div {
  font-family: 'Cairo', sans-serif;
  color: var(--text-secondary);
}

/* Parallax effect container */
.parallax-container {
  position: relative;
  overflow: hidden;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.parallax-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(255, 255, 255, 0.9) 100%);
  backdrop-filter: blur(2px);
}

/* Enhanced form inputs */
.client-theme input, .client-theme textarea, .client-theme select {
  border: 2px solid var(--primary-gold-light);
  border-radius: 8px;
  padding: 12px 16px;
  font-family: 'Cairo', sans-serif;
  transition: all 0.3s ease;
  background: var(--accent-white);
}

.client-theme input:focus, .client-theme textarea:focus, .client-theme select:focus {
  border-color: var(--primary-gold);
  box-shadow: 0 0 0 3px var(--shadow-gold);
  outline: none;
  transform: translateY(-1px);
}

/* Elegant table styling */
.client-theme table {
  background: var(--accent-white);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 25px var(--shadow-gold);
}

.client-theme th {
  background: var(--gradient-gold);
  color: var(--accent-white);
  font-family: 'Amiri', serif;
  font-weight: 700;
  padding: 16px;
}

.client-theme td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--primary-gold-light);
  font-family: 'Cairo', sans-serif;
  color: var(--text-secondary);
}

.client-theme tr:hover {
  background: var(--accent-cream);
  transition: all 0.3s ease;
}

/* Advanced visual effects */
.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(212, 175, 55, 0.2);
  box-shadow: 0 8px 32px rgba(212, 175, 55, 0.1);
}

.gold-gradient-text {
  background: var(--gradient-gold);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* Floating elements */
.floating {
  animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Elegant shadows */
.shadow-gold-lg {
  box-shadow: 0 20px 40px var(--shadow-gold);
}

.shadow-gold-xl {
  box-shadow: 0 25px 50px var(--shadow-gold);
}

/* Responsive design improvements */
@media (max-width: 768px) {
  .client-theme .topbar {
    padding: 8px 16px;
  }

  .client-theme aside {
    width: 100%;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .client-theme aside.open {
    transform: translateX(0);
  }

  .client-theme .card {
    margin: 8px;
    border-radius: 8px;
  }
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Status indicators */
.status-success {
  background: linear-gradient(135deg, #10b981, #34d399);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.status-warning {
  background: linear-gradient(135deg, #f59e0b, #fbbf24);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.status-error {
  background: linear-gradient(135deg, #ef4444, #f87171);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

/* Enhanced buttons */
.btn-gold {
  background: var(--gradient-gold);
  color: var(--accent-white);
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-family: 'Cairo', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px var(--shadow-gold);
}

.btn-gold:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow-gold);
  background: var(--primary-gold-dark);
}

.btn-outline-gold {
  background: transparent;
  color: var(--primary-gold);
  border: 2px solid var(--primary-gold);
  padding: 10px 22px;
  border-radius: 8px;
  font-weight: 600;
  font-family: 'Cairo', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-outline-gold:hover {
  background: var(--gradient-gold);
  color: var(--accent-white);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px var(--shadow-gold);
}

/* Modal and overlay improvements */
.modal-overlay {
  background: rgba(44, 24, 16, 0.7);
  backdrop-filter: blur(5px);
}

.modal-content {
  background: var(--accent-white);
  border-radius: 16px;
  box-shadow: 0 25px 50px rgba(44, 24, 16, 0.3);
  border: 2px solid var(--primary-gold-light);
}

/* Notification styles */
.notification {
  border-radius: 12px;
  padding: 16px 20px;
  margin: 8px 0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  animation: slideInRight 0.3s ease-out;
}

.notification-success {
  background: linear-gradient(135deg, #ecfdf5, #d1fae5);
  border-left: 4px solid #10b981;
  color: #065f46;
}

.notification-warning {
  background: linear-gradient(135deg, #fffbeb, #fef3c7);
  border-left: 4px solid #f59e0b;
  color: #92400e;
}

.notification-error {
  background: linear-gradient(135deg, #fef2f2, #fecaca);
  border-left: 4px solid #ef4444;
  color: #991b1b;
}

/* Arabic text enhancements */
.arabic-text {
  font-family: 'Amiri', serif;
  line-height: 1.8;
  letter-spacing: 0.5px;
}

.english-text {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
}

/* Print styles */
@media print {
  .client-theme {
    background: white !important;
  }

  .client-theme .topbar,
  .client-theme aside {
    display: none !important;
  }

  .client-theme .card {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}
