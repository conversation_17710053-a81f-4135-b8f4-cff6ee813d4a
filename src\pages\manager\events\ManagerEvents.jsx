import React from 'react'
import { listManagerEvents, addManagerEvent, editManagerEvent, deleteManagerEvent, getManagerEventsBundle } from '../../../api/manager'
import { formatSyrianDate } from '../../../utils/date'

function timesOverlap(aStart, aEnd, bStart, bEnd){
  return aStart < bEnd && bStart < aEnd
}

export default function ManagerEvents(){
  const [events, setEvents] = React.useState([])
  const [loading, setLoading] = React.useState(true)
  const [showForm, setShowForm] = React.useState(false)
  const [editingEvent, setEditingEvent] = React.useState(null)
  const [selectedDate, setSelectedDate] = React.useState(new Date().toISOString().split('T')[0])
  const [filter, setFilter] = React.useState('all')
  const [error, setError] = React.useState('')
  const [form, setForm] = React.useState({
    eventName: '',
    eventDate: '',
    startTime: '',
    endTime: '',
    eventType: '',
    chairsCount: 0,
    clientName: '',
    clientPhone: '',
    clientPassword: '',
    templateId: '',
    status: 'scheduled'
  })
  const [templates, setTemplates] = React.useState([])
  const [staffList, setStaffList] = React.useState([])

  React.useEffect(() => { 
    loadEvents()
  }, [])

  const loadEvents = async () => {
    setLoading(true)
    try {
      const bundle = await getManagerEventsBundle()
      // دعم تعدد أسماء الحقول في الريسبونس
      const rawEvents = bundle?.events || bundle?.bookings || bundle?.reservations || []
      // تطبيع الحقول لتوافق جدول العرض
      const normalizedEvents = rawEvents.map(ev => ({
        id: ev._id || ev.id,
        eventName: ev.eventName,
        eventDate: ev.eventDate,
        startTime: ev.startTime,
        endTime: ev.endTime,
        eventType: ev.eventType,
        chairsCount: ev.chairsCount ?? ev.numOfPeople ?? 0,
        clientName: ev.clientName || ev.clientId?.name || '',
        clientPhone: ev.clientPhone || ev.clientId?.phone || '',
        templateId: ev.templateId?._id || ev.templateId || '',
        // normalize any backend status to UI statuses: scheduled | cancelled
        status: ev.status || 'scheduled',
      }))
      setEvents(normalizedEvents)
      setTemplates(bundle?.templates || [])
      setStaffList(bundle?.staffList || [])
    } catch (error) {
      console.error('Error loading events:', error)
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setForm({
      eventName: '', eventDate: '', startTime: '', endTime: '', eventType: '',
      chairsCount: 0, clientName: '', clientEmail: '', clientPassword: '',
      templateId: '', status: 'scheduled'
    })
    setEditingEvent(null)
    setShowForm(false)
    setError('')
  }

  const handleEdit = (event) => {
    // تطبيع التاريخ لصيغة YYYY-MM-DD
    let eventDate = event.eventDate || '';
    if (eventDate && eventDate.length > 10) {
      eventDate = eventDate.slice(0, 10);
    }
    console.log('Editing event:', event, 'Normalized eventDate:', eventDate);
    setForm({
      eventName: event.eventName || '',
      eventDate: eventDate,
      startTime: event.startTime || '',
      endTime: event.endTime || '',
      eventType: event.eventType || 'wedding',
      chairsCount: event.chairsCount ?? event.numOfPeople ?? 0,
      clientName: event.clientName || '',
      clientPhone: event.clientPhone || event.phone || '',
      clientPassword: '',
      templateId: event.templateId || '',
      status: event.status || 'scheduled',
    })
    setEditingEvent(event)
    setShowForm(true)
  }

  const submit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      // overlap prevention (same date)
      const sameDate = events.filter(ev => ev.eventDate === form.eventDate && (!editingEvent || ev.id !== editingEvent.id))
      const start = form.startTime
      const end = form.endTime
      if (start && end && start >= end){
        setError('وقت البداية يجب أن يكون قبل وقت النهاية')
        setLoading(false)
        return
      }
      const hasOverlap = sameDate.some(ev => timesOverlap(start, end, ev.startTime, ev.endTime))
      if (hasOverlap){
        setError('هناك تداخل في المواعيد لهذا التاريخ')
        setLoading(false)
        return
      }

      if (editingEvent) {
        await editManagerEvent(editingEvent.id, form)
      } else {
        await addManagerEvent(form)
      }
      await loadEvents()
      resetForm()
      alert(editingEvent ? 'تم تحديث الحجز بنجاح' : 'تم إضافة الحجز بنجاح')
    } catch (error) {
      alert('حدث خطأ: ' + error.message)
    } finally {
      setLoading(false)
    }
  }

  const remove = async (id) => {
    if (!confirm('هل أنت متأكد من حذف هذا الحجز؟')) return
    
    try {
      await deleteManagerEvent(id)
      setEvents(events.filter(x => x.id !== id))
      alert('تم حذف الحجز بنجاح')
    } catch (error) {
      alert('حدث خطأ أثناء الحذف')
    }
  }

  const getStatusColor = (status) => {
    const statusColors = {
      'scheduled': 'bg-yellow-100 text-yellow-800',
      'confirmed': 'bg-green-100 text-green-800',
      'cancelled': 'bg-red-100 text-red-800',
      'completed': 'bg-blue-100 text-blue-800'
    }
    return statusColors[status] || 'bg-gray-100 text-gray-800'
  }

  const getStatusLabel = (status) => {
    const statusLabels = {
      'scheduled': 'في الانتظار', 'confirmed': 'مؤكد', 'cancelled': 'ملغي', 'completed': 'مكتمل'

    }
    return statusLabels[status] || status
  }

   const getEventColor = (event) => {
      const eventColors = {
        'wedding': 'bg-pink-100 text-pink-800',
        'engagement': 'bg-purple-100 text-purple-800',
        'birthday': 'bg-yellow-100 text-yellow-800',
        'graduation': 'bg-blue-100 text-blue-800',
        'other': 'bg-gray-100 text-gray-800'
      }
      return eventColors[event] || 'bg-gray-100 text-gray-800'
    }

    const getEventLabel = (event) => {
      const eventLabels = {
        'wedding': 'زفاف',
        'engagement': 'خطوبة',
        'birthday': 'عيد ميلاد',
        'graduation': 'تخرج',
        'other': 'أخرى'
      }
      return eventLabels[event] || event
    }

  // فلترة حسب حالتين فقط: مجدولة أو ملغية
  const filteredEvents = events.filter(event => {
    const status = event.status 
    const matchesFilter = filter === 'all' || status === filter
    const eventDay = event.eventDate ? event.eventDate.substring(0, 10) : ''
    const matchesDate = !selectedDate || eventDay === selectedDate
    return matchesFilter && matchesDate
  })

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">إدارة الحجوزات</h2>
        <button onClick={() => setShowForm(!showForm)} className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
          {showForm ? 'إلغاء' : 'إضافة حجز جديد'}
        </button>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">التاريخ</label>
            <input type="date" value={selectedDate} onChange={e => setSelectedDate(e.target.value)} className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
            <select value={filter} onChange={e => setFilter(e.target.value)} className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="all">جميع الحالات</option>
              <option value="scheduled">في الانتظار</option>
              <option value="confirmed">مؤكد</option>
              <option value="cancelled">ملغي</option>
              <option value="completed">مكتمل</option>
            </select>
          </div>
        </div>
        {error && <div className='mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded'>{error}</div>}
      </div>

      {/* Add/Edit Form */}
      {showForm && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold mb-4">
            {editingEvent ? 'تعديل الحجز' : 'إضافة حجز جديد'}
          </h3>
          
          <form onSubmit={submit} className="space-y-6">
            {/* Event Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  عنوان الحدث *
                </label>
                <input
                  type="text"
                  value={form.eventName}
                  onChange={e => setForm({...form, eventName: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  نوع الحدث *
                </label>
                <select
                  value={form.eventType}
                  onChange={e => setForm({...form, eventType: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="">اختر نوع الحدث</option>
                  <option value="wedding">زفاف</option>
                  <option value="engagement">خطوبة</option>
                  <option value="birthday">عيد ميلاد</option>
                  <option value="graduation">تخرج</option>
                  <option value="other">أخرى</option>
                </select>
              </div>
            </div>

            {/* Date and Time */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  تاريخ الحدث *
                </label>
                <input
                  type="date"
                  value={form.eventDate}
                  onChange={e => setForm({...form, eventDate: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  وقت البداية *
                </label>
                <input
                  type="time"
                  value={form.startTime}
                  onChange={e => setForm({...form, startTime: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  وقت النهاية *
                </label>
                <input
                  type="time"
                  value={form.endTime}
                  onChange={e => setForm({...form, endTime: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
            </div>

            {/* Capacity and Status */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  عدد الكراسي *
                </label>
                <input
                  type="number"
                  value={form.chairsCount}
                  onChange={e => setForm({...form, chairsCount: +e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  min="1"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                <select
                  value={form.status}
                  onChange={e => setForm({...form, status: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="scheduled">في الانتظار</option>
                  <option value="confirmed">مؤكد</option>
                  <option value="cancelled">ملغي</option>
                  <option value="completed">مكتمل</option>
                </select>
              </div>
            </div>

            {/* Client Information */}
            {!editingEvent && (
              <div className="border-t pt-6">
                <h4 className="text-md font-medium text-gray-900 mb-4">معلومات العميل</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      اسم العميل *
                    </label>
                    <input
                      type="text"
                      value={form.clientName}
                      onChange={e => setForm({...form, clientName: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      رقم الجوال *
                    </label>
                    <input
                      type="tel"
                      inputMode="numeric"
                      value={form.clientPhone}
                      onChange={e => setForm({...form, clientPhone: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      كلمة المرور {editingEvent ? '' : '*'}
                    </label>
                    <input
                      type="password"
                      value={form.clientPassword}
                      onChange={e => setForm({...form, clientPassword: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required={!editingEvent}
                    />
                  </div>
                </div>
              </div>
            )}
            {/* Template Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">القالب</label>
              <select value={form.templateId} onChange={e=> setForm({...form, templateId: e.target.value})} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="">بدون قالب</option>
                {templates.map(t=> (<option key={t._id || t.id} value={t._id || t.id}>{t.templateName || t.name}</option>))}
              </select>
            </div>

            

            {/* Form Actions */}
            <div className="flex gap-3 pt-4">
              <button
                type="submit"
                disabled={loading}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-blue-400 transition-colors"
              >
                {loading ? 'جاري الحفظ...' : (editingEvent ? 'تحديث الحجز' : 'إضافة الحجز')}
              </button>
              
              <button
                type="button"
                onClick={resetForm}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                إلغاء
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Events List */}
      <div className="bg-white rounded-lg shadow-md">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">قائمة الحجوزات</h3>
        </div>
        
        {loading ? (
          <div className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          </div>
        ) : filteredEvents.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            لا توجد حجوزات
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الحدث
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    العميل
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    التاريخ والوقت
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    السعة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredEvents.map(event => (
                  <tr key={event.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{event.eventName}</div>
                        <div className={`text-sm text-gray-600 ${getEventColor(event.eventType)}`}> {getEventLabel(event.eventType)}</div>
                      </div>
                    </td>
                    
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{event.clientName}</div>
                        <div className="text-sm text-gray-500">{event.clientEmail}</div>
                      </div>
                    </td>
                    
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{formatSyrianDate(event.eventDate)}</div>
                        <div className="text-sm text-gray-500">
                          {event.startTime} - {event.endTime}
                        </div>
                      </div>
                    </td>
                    
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {event.chairsCount} كرسي
                    </td>
                    
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(event.status)}`}>
                        {getStatusLabel(event.status)}
                      </span>
                    </td>
                    
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex gap-2">
                        <button
                          onClick={() => handleEdit(event)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          تعديل
                        </button>
                        
                        <button
                          onClick={() => remove(event.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          حذف
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-full">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">إجمالي الحجوزات</p>
              <p className="text-2xl font-semibold text-gray-900">{events.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-full">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">الحجوزات المجدولة</p>
              <p className="text-2xl font-semibold text-gray-900">{events.filter(e => e.status !== 'cancelled').length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-full">
              <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">الحجوزات الملغية</p>
              <p className="text-2xl font-semibold text-gray-900">{events.filter(e => e.status === 'cancelled').length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-full">
              <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">إجمالي الكراسي</p>
              <p className="text-2xl font-semibold text-gray-900">
                {events.reduce((sum, e) => sum + e.chairsCount, 0)}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}