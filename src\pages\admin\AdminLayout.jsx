import React from 'react'
import TopBar from '../../components/TopBar'
import Sidebar from '../../components/Sidebar'
import { Routes, Route } from 'react-router-dom'
import AdminHome from './AdminHome'
import Profile from '../Profile'
import AdminHalls from './halls/AdminHalls'
import AdminTemplates from './templates/AdminTemplates'
import AdminUsers from './users/AdminUsers'
import AdminManagers from './managers/AdminManagers'
import AdminComplaints from './complaints/AdminComplaints'
export default function AdminLayout(){
  const [open, setOpen] = React.useState(false)
  const items=[
    {to:'/admin',label:'الرئيسية'},
    {to:'/admin/halls',label:'الصالات'},
    {to:'/admin/templates',label:'القوالب'},
    {to:'/admin/users',label:'المستخدمون'},
    {to:'/admin/managers',label:'المدراء'},
    {to:'/admin/complaints',label:'الشكاوى'},
    {to:'/admin/profile',label:'الملف الشخصي'},
  ]
  return (
    <div className='client-theme min-h-screen flex'>
      <Sidebar items={items} isOpen={open} onClose={()=>setOpen(false)} />
      <div className='flex-1 md:pr-72'>
        <TopBar onToggleSidebar={()=>setOpen(v=>!v)} />
        <div className='p-4 sm:p-6 pt-16'>
          <Routes>
            <Route path='/' element={<AdminHome/>} />
            <Route path='profile' element={<Profile/>} />
            <Route path='halls/*' element={<AdminHalls/>} />
            <Route path='templates/*' element={<AdminTemplates/>} />
            <Route path='users/*' element={<AdminUsers/>} />
            <Route path='managers/*' element={<AdminManagers/>} />
            <Route path='complaints/*' element={<AdminComplaints/>} />
          </Routes>
        </div>
      </div>
    </div>
  )
}
